<?php
/**
 * Individual Service Detail Page
 * Redolence Medi Aesthetics - Dedicated page for each service
 */

require_once __DIR__ . '/config/app.php';

// Get service ID from URL parameter
$serviceId = $_GET['id'] ?? '';

if (empty($serviceId)) {
    header('Location: services.php');
    exit;
}

// Get service details
try {
    $service = $database->fetch(
        "SELECT * FROM services WHERE id = ? AND is_active = 1",
        [$serviceId]
    );

    if (!$service) {
        header('Location: services.php');
        exit;
    }
} catch (Exception $e) {
    header('Location: services.php');
    exit;
}

// Decode HTML entities in service data
$service['name'] = html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8');
$service['description'] = html_entity_decode($service['description'] ?? '', ENT_QUOTES, 'UTF-8');
$service['session_frequency'] = html_entity_decode($service['session_frequency'] ?? '', ENT_QUOTES, 'UTF-8');
$service['technology_used'] = html_entity_decode($service['technology_used'] ?? '', ENT_QUOTES, 'UTF-8');

// Get related services (same category)
$relatedServices = $database->fetchAll(
    "SELECT * FROM services WHERE category = ? AND id != ? AND is_active = 1 ORDER BY name LIMIT 3",
    [$service['category'], $serviceId]
);

// Set page title and description (like other pages do)
$pageTitle = $service['name'] . " - Medical Aesthetics Treatment";
$pageDescription = strip_tags(substr($service['description'] ?? '', 0, 160)) . "...";

include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced Service Detail Page Styles - Scoped to avoid header conflicts */
.service-detail-page {
    --redolence-green: #49a75c;
    --redolence-blue: #5894d2;
    --redolence-navy: #1a2332;
    --redolence-gold: #f4d03f;
}

/* Hero Section - Full Width Layout */
.service-detail-page .service-detail-hero {
    background: linear-gradient(135deg, var(--redolence-green) 0%, var(--redolence-blue) 100%);
    min-height: 60vh;
    position: relative;
    overflow: hidden;
}

.service-detail-page .service-detail-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.service-detail-page .service-content {
    position: relative;
    z-index: 10;
}

/* Enhanced Badges - Scoped */
.service-detail-page .service-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 700;
    margin-right: 0.75rem;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
}

.service-detail-page .badge-featured {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    animation: pulse 2s infinite;
}
.service-detail-page .badge-popular {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    animation: bounce 2s infinite;
}
.service-detail-page .badge-new {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    animation: glow 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
    50% { box-shadow: 0 4px 25px rgba(16, 185, 129, 0.4); }
}

/* Full Width Sections - Like Index Page */
.service-detail-page .content-section {
    padding: 4rem 0;
    position: relative;
}

.service-detail-page .content-section.bg-gray {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.service-detail-page .content-section.bg-white {
    background: white;
}

.service-detail-page .section-container {
    max-width: 7xl;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Content Cards - Redesigned */
.service-detail-page .content-card {
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    padding: 2.5rem;
    border: 1px solid rgba(73, 167, 92, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.service-detail-page .content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

/* Enhanced CTA Styles - Scoped */
.service-detail-page .cta-card {
    background: linear-gradient(135deg, var(--redolence-green) 0%, var(--redolence-blue) 100%);
    border-radius: 2rem;
    padding: 4rem 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin: 3rem 0;
    box-shadow: 0 25px 50px -12px rgba(73, 167, 92, 0.3);
}

.service-detail-page .cta-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.service-detail-page .cta-content {
    position: relative;
    z-index: 10;
}

.service-detail-page .cta-button {
    display: inline-flex;
    align-items: center;
    background: white;
    color: var(--redolence-green);
    padding: 1.25rem 2.5rem;
    border-radius: 50px;
    font-weight: 800;
    font-size: 1.25rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    border: 3px solid transparent;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.service-detail-page .cta-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    background: var(--redolence-gold);
    color: var(--redolence-navy);
}

/* Enhanced Related Services - Scoped */
.service-detail-page .related-service-card {
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    cursor: pointer;
    border: 2px solid transparent;
    overflow: hidden;
    position: relative;
}

.service-detail-page .related-service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(73, 167, 92, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-detail-page .related-service-card:hover::before {
    left: 100%;
}

.service-detail-page .related-service-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border-color: var(--redolence-green);
}

/* Content Styling - Scoped */
.service-detail-page .service-description {
    font-size: 1.125rem;
    line-height: 1.8;
    color: #374151;
}

.service-detail-page .service-description h3 {
    color: var(--redolence-navy);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 2rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--redolence-green);
}

.service-detail-page .service-description h4 {
    color: var(--redolence-green);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
}

.service-detail-page .service-description ul {
    list-style: none;
    padding-left: 0;
    margin: 1rem 0;
}

.service-detail-page .service-description li {
    padding: 0.5rem 0;
    padding-left: 2rem;
    position: relative;
}

.service-detail-page .service-description li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--redolence-green);
    font-weight: bold;
    font-size: 1.2rem;
}

.service-detail-page .service-description p {
    margin: 1rem 0;
}

.service-detail-page .service-description strong {
    color: var(--redolence-navy);
    font-weight: 600;
}

/* Responsive Design - Full Width Layout */
@media (max-width: 1024px) {
    .service-detail-page .section-container {
        padding: 0 1rem;
    }
}

@media (max-width: 768px) {
    .service-detail-page .content-section {
        padding: 3rem 0;
    }

    .service-detail-page .content-card {
        padding: 2rem 1.5rem;
    }

    .service-detail-page .cta-card {
        padding: 3rem 2rem;
    }

    .service-detail-page .service-detail-hero {
        min-height: 50vh;
    }

    .service-detail-page .section-container {
        padding: 0 1rem;
    }
}
</style>

<!-- Service Detail Page Container -->
<div class="service-detail-page">

<!-- Service Detail Hero - Full Width -->
<section class="service-detail-hero">
    <div class="section-container">
        <div class="grid grid-cols-12 gap-8 items-center min-h-[60vh] py-16">

            <!-- Left Content - Service Info -->
            <div class="col-span-12 lg:col-span-7 service-content text-white">
                <!-- Breadcrumb -->
                <nav class="mb-6">
                    <ol class="flex items-center space-x-2 text-white/80 text-sm">
                        <li><a href="index.php" class="hover:text-white transition-colors">Home</a></li>
                        <li><span class="mx-2">/</span></li>
                        <li><a href="services.php" class="hover:text-white transition-colors">Services</a></li>
                        <li><span class="mx-2">/</span></li>
                        <li class="text-white font-medium"><?= $service['name'] ?></li>
                    </ol>
                </nav>

                <!-- Service Badges -->
                <div class="mb-6">
                    <?php if ($service['featured']): ?>
                        <span class="service-badge badge-featured">⭐ Featured</span>
                    <?php endif; ?>
                    <?php if ($service['popular']): ?>
                        <span class="service-badge badge-popular">🔥 Popular</span>
                    <?php endif; ?>
                    <?php if ($service['new_treatment']): ?>
                        <span class="service-badge badge-new">✨ New</span>
                    <?php endif; ?>
                </div>

                <!-- Service Title -->
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                    <?= $service['name'] ?>
                </h1>

                <!-- Service Summary -->
                <p class="text-lg md:text-xl text-white/90 mb-8 leading-relaxed">
                    <?= strip_tags(substr($service['description'] ?? '', 0, 200)) ?>...
                </p>

                <!-- Quick Info Cards -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                    <?php if ($service['price']): ?>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2 text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <span class="font-semibold">TSH <?= number_format($service['price']) ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($service['duration']): ?>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="font-semibold"><?= $service['duration'] ?> minutes</span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- CTA Button -->
                <div>
                    <a href="customer/book?service=<?= $service['id'] ?>"
                       class="inline-flex items-center bg-white text-redolence-green px-8 py-4 rounded-2xl font-bold text-lg transition-all hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Book This Treatment
                    </a>
                </div>
            </div>

            <!-- Right Content - Service Image -->
            <div class="col-span-12 lg:col-span-5">
                <?php if ($service['image']): ?>
                    <div class="relative">
                        <?php
                        $imageSrc = $service['image'];
                        if (!filter_var($imageSrc, FILTER_VALIDATE_URL)) {
                            $imageSrc = getBasePath() . '/uploads/' . ltrim($imageSrc, '/');
                        }
                        ?>
                        <img src="<?= htmlspecialchars($imageSrc) ?>"
                             alt="<?= $service['name'] ?>"
                             class="w-full h-80 lg:h-96 object-cover rounded-2xl shadow-2xl">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Service Description Section -->
<section class="content-section bg-white">
    <div class="section-container">
        <div class="grid grid-cols-12 gap-8">
            <div class="col-span-12 lg:col-span-8">
                <div class="content-card">
                    <h2 class="text-3xl font-bold text-redolence-navy mb-6 flex items-center">
                        <svg class="w-8 h-8 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        About This Treatment
                    </h2>
                    <div class="service-description">
                        <?= $service['description'] ?>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Sidebar -->
            <div class="col-span-12 lg:col-span-4">
                <div class="content-card bg-gradient-to-br from-redolence-green/5 to-redolence-blue/5">
                    <h3 class="text-xl font-bold text-redolence-navy mb-6">Quick Facts</h3>
                    <div class="space-y-4">
                        <?php if ($service['price']): ?>
                            <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                                <span class="text-gray-600">Starting Price</span>
                                <span class="font-bold text-redolence-green">TSH <?= number_format($service['price']) ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if ($service['duration']): ?>
                            <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                                <span class="text-gray-600">Duration</span>
                                <span class="font-bold text-redolence-blue"><?= $service['duration'] ?> min</span>
                            </div>
                        <?php endif; ?>

                        <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                            <span class="text-gray-600">Category</span>
                            <span class="font-bold text-redolence-navy capitalize"><?= $service['category'] ?></span>
                        </div>

                        <div class="mt-6">
                            <a href="customer/book?service=<?= $service['id'] ?>"
                               class="w-full inline-flex items-center justify-center bg-redolence-green text-white px-6 py-3 rounded-xl font-bold transition-all hover:bg-redolence-green/90 hover:scale-105">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Book Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Treatment Details Section -->
<?php if ($service['session_frequency'] || $service['technology_used']): ?>
<section class="content-section bg-gray">
    <div class="section-container">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-redolence-navy mb-4">Treatment Details</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">Everything you need to know about this professional treatment</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-<?= ($service['session_frequency'] && $service['technology_used']) ? '2' : '1' ?> gap-8 max-w-4xl mx-auto">

            <!-- Session Frequency -->
            <?php if ($service['session_frequency']): ?>
                <div class="content-card bg-gradient-to-br from-yellow-50 to-yellow-100 border-2 border-yellow-200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-yellow-800">Recommended Frequency</h3>
                    </div>
                    <p class="text-lg text-yellow-900 font-semibold leading-relaxed"><?= $service['session_frequency'] ?></p>
                </div>
            <?php endif; ?>

            <!-- Technology Used -->
            <?php if ($service['technology_used']): ?>
                <div class="content-card bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-purple-800">Technology Used</h3>
                    </div>
                    <p class="text-lg text-purple-900 font-semibold leading-relaxed"><?= $service['technology_used'] ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Enhanced Book Now CTA Section -->
<section class="content-section">
    <div class="cta-card">
        <div class="section-container">
            <div class="cta-content text-center">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8 backdrop-blur-sm">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>

                <h2 class="text-4xl md:text-5xl font-black text-white mb-6 leading-tight">
                    Ready to Begin Your<br>
                    <span class="text-yellow-300">Transformation?</span>
                </h2>

                <p class="text-xl md:text-2xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed">
                    Book your personalized consultation with our board-certified medical specialists and discover the perfect treatment plan for your aesthetic goals.
                </p>

                <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
                    <a href="customer/book?service=<?= $service['id'] ?>" class="cta-button">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Book This Treatment
                    </a>

                    <div class="flex items-center text-white/90">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-lg font-semibold">Free consultation • No obligation • Expert guidance</span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                    <div class="bg-white/10 rounded-2xl p-6 backdrop-blur-sm border border-white/20">
                        <div class="text-3xl font-black text-white mb-2">500+</div>
                        <div class="text-white/80 font-medium">Successful Treatments</div>
                    </div>
                    <div class="bg-white/10 rounded-2xl p-6 backdrop-blur-sm border border-white/20">
                        <div class="text-3xl font-black text-white mb-2">98%</div>
                        <div class="text-white/80 font-medium">Patient Satisfaction</div>
                    </div>
                    <div class="bg-white/10 rounded-2xl p-6 backdrop-blur-sm border border-white/20">
                        <div class="text-3xl font-black text-white mb-2">5★</div>
                        <div class="text-white/80 font-medium">Average Rating</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
    </div>
</section>

<!-- Related Services Section -->
<?php if (!empty($relatedServices)): ?>
<section class="content-section bg-gray">
    <div class="section-container">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-redolence-navy mb-4">Related Treatments</h2>
            <p class="text-lg text-gray-600">Explore other treatments in the <span class="capitalize font-semibold text-redolence-green"><?= $service['category'] ?></span> category</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($relatedServices as $relatedService): ?>
                <div class="related-service-card content-card cursor-pointer group" onclick="window.location.href='service-detail.php?id=<?= $relatedService['id'] ?>'">
                    <?php if ($relatedService['image']): ?>
                        <?php
                        $relatedImageSrc = $relatedService['image'];
                        if (!filter_var($relatedImageSrc, FILTER_VALIDATE_URL)) {
                            $relatedImageSrc = getBasePath() . '/uploads/' . ltrim($relatedImageSrc, '/');
                        }
                        ?>
                        <div class="relative overflow-hidden rounded-xl mb-4">
                            <img src="<?= htmlspecialchars($relatedImageSrc) ?>"
                                 alt="<?= htmlspecialchars(html_entity_decode($relatedService['name'], ENT_QUOTES, 'UTF-8')) ?>"
                                 class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    <?php endif; ?>

                    <h3 class="text-xl font-bold text-redolence-navy mb-2 group-hover:text-redolence-green transition-colors duration-300"><?= htmlspecialchars(html_entity_decode($relatedService['name'], ENT_QUOTES, 'UTF-8')) ?></h3>
                    <p class="text-gray-600 mb-4 line-clamp-3"><?= strip_tags(substr($relatedService['description'] ?? '', 0, 120)) ?>...</p>

                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                        <?php if ($relatedService['price']): ?>
                            <span class="text-redolence-green font-bold text-lg">TSH <?= number_format($relatedService['price']) ?></span>
                        <?php else: ?>
                            <span class="text-gray-500 font-medium">Price on consultation</span>
                        <?php endif; ?>

                        <div class="flex items-center text-redolence-blue font-medium group-hover:text-redolence-green transition-colors duration-300">
                            <span class="mr-1">Learn More</span>
                            <svg class="w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

</div>
<!-- End Service Detail Page Container -->

<?php include __DIR__ . '/includes/footer.php'; ?>
