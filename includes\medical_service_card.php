<?php
/**
 * Medical Service Card Template
 * Enhanced card for displaying medical aesthetics treatments
 */

// Ensure $service variable is available
if (!isset($service)) {
    return;
}

// Prepare service data
$serviceName = html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8'); // Decode HTML entities
$serviceDescription = $service['description'] ?? '';
$servicePrice = $service['price'];
$serviceDuration = $service['duration'];
$sessionFrequency = htmlspecialchars($service['session_frequency'] ?? '');
$technologyUsed = htmlspecialchars($service['technology_used'] ?? '');
$featured = $service['featured'] ?? false;
$popular = $service['popular'] ?? false;
$newTreatment = $service['new_treatment'] ?? false;

// Prepare image
$serviceImage = $service['image'] ?? '';
$imageSrc = '';
if ($serviceImage) {
    if (strpos($serviceImage, 'http') === 0) {
        $imageSrc = $serviceImage;
    } else {
        $imageSrc = getBasePath() . '/uploads/' . $serviceImage;
    }
} else {
    // Default medical aesthetics image
    $imageSrc = getBasePath() . '/assets/images/default-medical-treatment.jpg';
}

// Format price display
$priceDisplay = $servicePrice ? formatCurrency($servicePrice) : '<span class="text-redolence-blue font-semibold">TSH</span>';

// Format duration display
$durationDisplay = $serviceDuration ? $serviceDuration . ' mins' : '<span class="text-gray-500">Variable</span>';

// Extract plain text from HTML description for preview
// First decode HTML entities, then strip tags
$decodedDescription = html_entity_decode($serviceDescription, ENT_QUOTES, 'UTF-8');
$plainDescription = strip_tags($decodedDescription);
$shortDescription = strlen($plainDescription) > 120 ? substr($plainDescription, 0, 120) . '...' : $plainDescription;
?>

<div class="medical-service-card group bg-white rounded-3xl p-6 border-2 border-gray-100 hover:border-redolence-green/30 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 cursor-pointer backdrop-blur-sm" onclick="openServiceModal('<?= $service['id'] ?>')" style="box-shadow: 0 4px 20px rgba(73, 167, 92, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);"
     onmouseover="this.style.boxShadow='0 20px 40px rgba(73, 167, 92, 0.15), 0 8px 16px rgba(88, 148, 210, 0.1)'"
     onmouseout="this.style.boxShadow='0 4px 20px rgba(73, 167, 92, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1)'"
>
    <!-- Treatment Badges -->
    <?php if ($featured || $popular || $newTreatment): ?>
        <div class="flex flex-wrap gap-2 mb-4">
            <?php if ($featured): ?>
                <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                    ⭐ Featured
                </span>
            <?php endif; ?>
            <?php if ($popular): ?>
                <span class="bg-gradient-to-r from-red-400 to-red-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                    🔥 Popular
                </span>
            <?php endif; ?>
            <?php if ($newTreatment): ?>
                <span class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                    ✨ New
                </span>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Service Image -->
    <div class="relative mb-6 overflow-hidden rounded-2xl">
        <img src="<?= $imageSrc ?>" 
             alt="<?= $serviceName ?>" 
             class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
             loading="lazy"
             onerror="this.src='<?= getBasePath() ?>/assets/images/default-medical-treatment.jpg'">
        
        <!-- Enhanced Overlay with brand colors -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-redolence-green/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
            <div class="absolute bottom-4 left-4 right-4">
                <div class="flex justify-between items-end text-white">
                    <div>
                        <div class="text-sm font-semibold"><?= $durationDisplay ?></div>
                        <?php if ($sessionFrequency): ?>
                            <div class="text-xs opacity-90"><?= $sessionFrequency ?></div>
                        <?php endif; ?>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold"><?= $priceDisplay ?></div>
                        <?php if ($servicePrice): ?>
                            <div class="text-xs opacity-90">Starting from</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Content -->
    <div class="space-y-4">
        <!-- Service Name -->
        <h3 class="text-xl font-bold text-gray-900 group-hover:text-redolence-green transition-colors duration-300">
            <?= $serviceName ?>
        </h3>

        <!-- Technology Used -->
        <?php if ($technologyUsed): ?>
            <div class="flex items-center text-sm text-redolence-blue bg-redolence-blue/10 px-3 py-2 rounded-lg">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
                <?= $technologyUsed ?>
            </div>
        <?php endif; ?>

        <!-- Service Description -->
        <?php if (!empty($serviceDescription)): ?>
            <div class="text-gray-600 leading-relaxed treatment-description">
                <?= $shortDescription ?>
            </div>
        <?php endif; ?>

        <!-- Treatment Details -->
        <div class="grid grid-cols-2 gap-4 py-4 border-t border-gray-100">
            <div class="text-center">
                <div class="text-sm text-gray-500">Duration</div>
                <div class="font-semibold text-gray-900"><?= $durationDisplay ?></div>
            </div>
            <div class="text-center">
                <div class="text-sm text-gray-500">Price</div>
                <div class="font-semibold text-gray-900"><?= $priceDisplay ?></div>
            </div>
        </div>

        <!-- Session Frequency -->
        <?php if ($sessionFrequency): ?>
            <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-xs text-gray-500 mb-1">Recommended Frequency</div>
                <div class="text-sm font-medium text-gray-900"><?= $sessionFrequency ?></div>
            </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="flex gap-3 pt-4">
            <button onclick="event.stopPropagation(); openServiceModal('<?= $service['id'] ?>')"
                    class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                Learn More
            </button>
            <a href="<?= getBasePath() ?>/customer/book?service=<?= $service['id'] ?>"
               onclick="event.stopPropagation()"
               class="bg-white hover:bg-redolence-blue text-redolence-blue hover:text-white border-2 border-redolence-blue py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                Book Now
            </a>
        </div>
    </div>
</div>
