<?php
/**
 * Customer Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Get customer statistics
 */
function getCustomerStats() {
    global $database;
    
    $stats = [];
    
    // Total customers
    $stats['total'] = $database->fetch(
        "SELECT COUNT(*) as count FROM users WHERE role = 'CUSTOMER'"
    )['count'];
    
    // New customers this month
    $stats['new_this_month'] = $database->fetch(
        "SELECT COUNT(*) as count FROM users 
         WHERE role = 'CUSTOMER' 
         AND YEAR(created_at) = YEAR(CURDATE()) 
         AND MONTH(created_at) = MONTH(CURDATE())"
    )['count'];
    
    // Average lifetime value
    $stats['avg_lifetime_value'] = $database->fetch(
        "SELECT AVG(total_spent) as avg_value FROM (
            SELECT u.id, COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_spent
            FROM users u
            LEFT JOIN bookings b ON u.id = b.user_id
            WHERE u.role = 'CUSTOMER'
            GROUP BY u.id
         ) as customer_totals"
    )['avg_value'] ?? 0;
    
    // Total points issued
    $stats['total_points'] = $database->fetch(
        "SELECT COALESCE(SUM(points), 0) as total FROM users WHERE role = 'CUSTOMER'"
    )['total'];
    
    // Active customers (booked in last 3 months)
    $stats['active_customers'] = $database->fetch(
        "SELECT COUNT(DISTINCT u.id) as count 
         FROM users u
         INNER JOIN bookings b ON u.id = b.user_id
         WHERE u.role = 'CUSTOMER' 
         AND b.date >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)"
    )['count'];
    
    // Top customers by spending
    $stats['top_customers'] = $database->fetchAll(
        "SELECT u.name, u.email, COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_spent
         FROM users u
         LEFT JOIN bookings b ON u.id = b.user_id
         WHERE u.role = 'CUSTOMER'
         GROUP BY u.id, u.name, u.email
         ORDER BY total_spent DESC
         LIMIT 5"
    );
    
    return $stats;
}

/**
 * Update customer points
 */
function updateCustomerPoints($customerId, $action, $points, $reason = '') {
    global $database;
    
    try {
        // Get current customer data
        $customer = $database->fetch(
            "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
            [$customerId]
        );
        
        if (!$customer) {
            return ['success' => false, 'error' => 'Customer not found'];
        }
        
        $currentPoints = $customer['points'];
        $newPoints = $currentPoints;
        
        switch ($action) {
            case 'add':
                $newPoints = $currentPoints + $points;
                break;
            case 'subtract':
                $newPoints = max(0, $currentPoints - $points);
                break;
            case 'set':
                $newPoints = $points;
                break;
            default:
                return ['success' => false, 'error' => 'Invalid action'];
        }
        
        // Update customer points
        $database->query(
            "UPDATE users SET points = ?, updated_at = NOW() WHERE id = ?",
            [$newPoints, $customerId]
        );
        
        // Log the points transaction
        logPointsTransaction($customerId, $action, $points, $currentPoints, $newPoints, $reason);
        
        return ['success' => true, 'new_points' => $newPoints];
        
    } catch (Exception $e) {
        error_log("Customer points update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update points'];
    }
}

/**
 * Log points transaction
 */
function logPointsTransaction($customerId, $action, $amount, $oldPoints, $newPoints, $reason = '') {
    global $database;
    
    try {
        $transactionId = generateUUID();
        
        $database->query(
            "INSERT INTO points_transactions (id, user_id, action, amount, old_points, new_points, reason, created_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, NOW())",
            [
                $transactionId,
                $customerId,
                $action,
                $amount,
                $oldPoints,
                $newPoints,
                $reason
            ]
        );
        
        return true;
        
    } catch (Exception $e) {
        error_log("Points transaction log error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get customer details with booking history
 */
function getCustomerDetails($customerId) {
    global $database;

    try {
        // Validate input
        if (empty($customerId)) {
            return null;
        }

        // Get customer basic info - try multiple approaches
        $customer = null;

        // First try with role check
        try {
            $customer = $database->fetch(
                "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
                [$customerId]
            );
        } catch (Exception $e) {
            // If role query failed, try without role check
        }

        // If that failed, try without role check to see if user exists
        if (!$customer) {
            try {
                $anyUser = $database->fetch(
                    "SELECT * FROM users WHERE id = ?",
                    [$customerId]
                );

                if ($anyUser && $anyUser['role'] === 'CUSTOMER') {
                    $customer = $anyUser;
                } else {
                    return null;
                }
            } catch (Exception $e) {
                return null;
            }
        }

        if (!$customer) {
            return null;
        }

        // Ensure points field exists and has a default value
        if (!isset($customer['points'])) {
            $customer['points'] = 0;
        }
        
        // Get booking statistics with error handling
        $bookingStats = null;
        try {
            $bookingStats = $database->fetch(
                "SELECT
                    COUNT(*) as total_bookings,
                    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_bookings,
                    COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_bookings,
                    COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as total_spent,
                    MAX(date) as last_booking_date,
                    MIN(date) as first_booking_date
                 FROM bookings
                 WHERE user_id = ?",
                [$customerId]
            );
        } catch (Exception $e) {
            // Ignore booking stats errors
        }

        // Ensure we have default values
        if (!$bookingStats) {
            $bookingStats = [
                'total_bookings' => 0,
                'completed_bookings' => 0,
                'cancelled_bookings' => 0,
                'total_spent' => 0,
                'last_booking_date' => null,
                'first_booking_date' => null
            ];
        }
        
        // Get recent bookings with error handling
        $recentBookings = [];
        try {
            $recentBookings = $database->fetchAll(
                "SELECT b.*, s.name as service_name, st.name as staff_name, p.name as package_name, p.manual_services
                 FROM bookings b
                 LEFT JOIN services s ON b.service_id = s.id
                 LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
                 LEFT JOIN packages p ON b.package_id = p.id
                 WHERE b.user_id = ?
                 ORDER BY b.date DESC, b.start_time DESC
                 LIMIT 10",
                [$customerId]
            );

            // Process bookings to handle custom packages with manual services
            foreach ($recentBookings as &$booking) {
                if (empty($booking['service_name']) && !empty($booking['package_name'])) {
                    // This is a package booking
                    if (!empty($booking['manual_services'])) {
                        // Custom package with manual services
                        $manualServices = json_decode($booking['manual_services'], true);
                        if (is_array($manualServices) && !empty($manualServices)) {
                            // Use the first manual service name or create a descriptive name
                            if (count($manualServices) === 1) {
                                $booking['service_name'] = $manualServices[0]['name'] ?? $booking['package_name'];
                            } else {
                                $booking['service_name'] = $booking['package_name'] . ' (' . count($manualServices) . ' services)';
                            }
                        } else {
                            $booking['service_name'] = $booking['package_name'];
                        }
                    } else {
                        // Regular package
                        $booking['service_name'] = $booking['package_name'];
                    }
                } elseif (empty($booking['service_name'])) {
                    // Fallback for any other cases
                    $booking['service_name'] = 'Service Booking';
                }
            }
        } catch (Exception $e) {
            $recentBookings = [];
        }

        // Get points history with error handling
        $pointsHistory = [];
        try {
            $pointsHistory = $database->fetchAll(
                "SELECT * FROM point_transactions
                 WHERE user_id = ?
                 ORDER BY created_at DESC
                 LIMIT 20",
                [$customerId]
            );
        } catch (Exception $e) {
            $pointsHistory = [];
        }

        // Get favorite services and packages with error handling
        $favoriteServices = [];
        try {
            // Get favorite services
            $favoriteServicesData = $database->fetchAll(
                "SELECT s.name, COUNT(*) as booking_count, 'Service' as type
                 FROM bookings b
                 INNER JOIN services s ON b.service_id = s.id
                 WHERE b.user_id = ? AND b.status = 'COMPLETED'
                 GROUP BY s.id, s.name
                 ORDER BY booking_count DESC
                 LIMIT 10",
                [$customerId]
            );

            // Get favorite packages
            $favoritePackagesData = $database->fetchAll(
                "SELECT p.name, COUNT(*) as booking_count, 'Package' as type
                 FROM bookings b
                 INNER JOIN packages p ON b.package_id = p.id
                 WHERE b.user_id = ? AND b.status = 'COMPLETED'
                 GROUP BY p.id, p.name
                 ORDER BY booking_count DESC
                 LIMIT 10",
                [$customerId]
            );

            // Combine and sort by booking count
            $favoriteServices = array_merge($favoriteServicesData, $favoritePackagesData);
            usort($favoriteServices, function($a, $b) {
                return $b['booking_count'] - $a['booking_count'];
            });

            // Limit to top 5
            $favoriteServices = array_slice($favoriteServices, 0, 5);

        } catch (Exception $e) {
            $favoriteServices = [];
        }

        return [
            'customer' => $customer,
            'stats' => $bookingStats,
            'recent_bookings' => $recentBookings,
            'points_history' => $pointsHistory,
            'favorite_services' => $favoriteServices
        ];

    } catch (Exception $e) {
        return null;
    }
}

/**
 * Export customers to CSV
 */
function exportCustomersCSV($filters = []) {
    global $database;
    
    try {
        $whereClause = "WHERE role = 'CUSTOMER'";
        $params = [];
        
        if (!empty($filters['search'])) {
            $whereClause .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }
        
        $customers = $database->fetchAll(
            "SELECT u.*, 
                    COUNT(DISTINCT b.id) as total_bookings,
                    COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_spent,
                    MAX(b.date) as last_booking_date
             FROM users u
             LEFT JOIN bookings b ON u.id = b.user_id
             $whereClause
             GROUP BY u.id
             ORDER BY u.created_at DESC",
            $params
        );
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="customers_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'ID',
            'Name',
            'Email',
            'Phone',
            'Points',
            'Total Bookings',
            'Total Spent',
            'Last Booking',
            'Date Joined'
        ]);
        
        // CSV data
        foreach ($customers as $customer) {
            fputcsv($output, [
                $customer['id'],
                $customer['name'],
                $customer['email'],
                $customer['phone'] ?? '',
                $customer['points'],
                $customer['total_bookings'],
                $customer['total_spent'],
                $customer['last_booking_date'] ? date('Y-m-d', strtotime($customer['last_booking_date'])) : '',
                date('Y-m-d', strtotime($customer['created_at']))
            ]);
        }
        
        fclose($output);
        exit;
        
    } catch (Exception $e) {
        error_log("Customer export error: " . $e->getMessage());
        return false;
    }
}



/**
 * Get customer loyalty tier
 */
function getCustomerLoyaltyTier($totalSpent) {
    if ($totalSpent >= 1000) {
        return ['tier' => 'Platinum', 'color' => 'text-purple-400', 'discount' => 15];
    } elseif ($totalSpent >= 500) {
        return ['tier' => 'Gold', 'color' => 'text-salon-gold', 'discount' => 10];
    } elseif ($totalSpent >= 200) {
        return ['tier' => 'Silver', 'color' => 'text-gray-400', 'discount' => 5];
    } else {
        return ['tier' => 'Bronze', 'color' => 'text-orange-400', 'discount' => 0];
    }
}

/**
 * Calculate customer retention rate
 */
function getCustomerRetentionRate($months = 12) {
    global $database;
    
    try {
        // Get customers who made their first booking X months ago
        $firstTimeCustomers = $database->fetch(
            "SELECT COUNT(DISTINCT user_id) as count
             FROM bookings b1
             WHERE b1.date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
             AND b1.date <= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
             AND NOT EXISTS (
                 SELECT 1 FROM bookings b2 
                 WHERE b2.user_id = b1.user_id 
                 AND b2.date < b1.date
             )",
            [$months, $months - 1]
        )['count'];
        
        // Get how many of those customers returned
        $returningCustomers = $database->fetch(
            "SELECT COUNT(DISTINCT b1.user_id) as count
             FROM bookings b1
             WHERE b1.date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
             AND b1.date <= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
             AND NOT EXISTS (
                 SELECT 1 FROM bookings b2 
                 WHERE b2.user_id = b1.user_id 
                 AND b2.date < b1.date
             )
             AND EXISTS (
                 SELECT 1 FROM bookings b3
                 WHERE b3.user_id = b1.user_id
                 AND b3.date > b1.date
             )",
            [$months, $months - 1]
        )['count'];
        
        if ($firstTimeCustomers > 0) {
            return round(($returningCustomers / $firstTimeCustomers) * 100, 2);
        }
        
        return 0;
        
    } catch (Exception $e) {
        error_log("Customer retention calculation error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Delete customer (soft delete - mark as inactive)
 */
function deleteCustomer($customerId) {
    global $database;

    try {
        // Check if customer exists
        $customer = $database->fetch(
            "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
            [$customerId]
        );

        if (!$customer) {
            return ['success' => false, 'error' => 'Customer not found'];
        }

        // Check if customer has active bookings
        $activeBookings = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings
             WHERE user_id = ? AND status IN ('PENDING', 'CONFIRMED') AND date >= CURDATE()",
            [$customerId]
        )['count'];

        if ($activeBookings > 0) {
            return ['success' => false, 'error' => 'Cannot delete customer with active bookings'];
        }

        // For now, we'll do a hard delete since is_active column doesn't exist yet
        // In production, you should add the is_active column first
        $database->query(
            "DELETE FROM users WHERE id = ?",
            [$customerId]
        );

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Customer deletion error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to delete customer'];
    }
}

/**
 * Create new customer
 */
function createCustomer($data) {
    global $database;

    try {
        // Validate required fields
        $requiredFields = ['name', 'email', 'password'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return ['success' => false, 'error' => "Field '$field' is required"];
            }
        }

        // Validate email format
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return ['success' => false, 'error' => 'Invalid email format'];
        }

        // Check if email already exists
        $existingUser = $database->fetch(
            "SELECT id FROM users WHERE email = ?",
            [$data['email']]
        );

        if ($existingUser) {
            return ['success' => false, 'error' => 'Email address already exists'];
        }

        // Validate password
        if (strlen($data['password']) < 6) {
            return ['success' => false, 'error' => 'Password must be at least 6 characters'];
        }

        if ($data['password'] !== $data['confirm_password']) {
            return ['success' => false, 'error' => 'Passwords do not match'];
        }

        // Generate customer ID
        $customerId = generateUUID();

        // Hash password
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);

        // Prepare customer data
        $customerData = [
            'id' => $customerId,
            'name' => cleanText($data['name']),
            'email' => sanitize($data['email']),
            'password' => $hashedPassword,
            'phone' => cleanText($data['phone'] ?? ''),
            'date_of_birth' => !empty($data['date_of_birth']) ? $data['date_of_birth'] : null,
            'role' => 'CUSTOMER',
            'points' => (int)($data['points'] ?? 0),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Insert customer (without is_active column for now)
        $database->query(
            "INSERT INTO users (id, name, email, password, phone, date_of_birth, role, points, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $customerData['id'],
                $customerData['name'],
                $customerData['email'],
                $customerData['password'],
                $customerData['phone'],
                $customerData['date_of_birth'],
                $customerData['role'],
                $customerData['points'],
                $customerData['created_at'],
                $customerData['updated_at']
            ]
        );

        // Send welcome email if requested
        if (!empty($data['send_welcome_email'])) {
            // Welcome email functionality would be implemented here
            // sendWelcomeEmail($customerData['email'], $customerData['name']);
        }

        // Create notification for new customer
        require_once __DIR__ . '/notification_triggers.php';
        createCustomerNotification($customerId, 'CUSTOMER_NEW');

        return ['success' => true, 'id' => $customerId];

    } catch (Exception $e) {
        error_log("Customer creation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create customer'];
    }
}

/**
 * Send message to customer
 */
function sendCustomerMessage($customerId, $data) {
    global $database;

    try {
        // Validate required fields
        if (empty($data['message'])) {
            return ['success' => false, 'error' => 'Message content is required'];
        }

        if (empty($data['message_type'])) {
            return ['success' => false, 'error' => 'Message type is required'];
        }

        // Get customer details
        $customer = $database->fetch(
            "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
            [$customerId]
        );

        if (!$customer) {
            return ['success' => false, 'error' => 'Customer not found'];
        }

        // Process message content with variables
        $messageContent = $data['message'];
        $messageContent = str_replace('{customer_name}', $customer['name'], $messageContent);
        $messageContent = str_replace('{salon_name}', 'Flix Salonce', $messageContent);
        $messageContent = str_replace('{points_balance}', number_format($customer['points']), $messageContent);

        // Generate message ID
        $messageId = generateUUID();

        // Save message to database
        $database->query(
            "INSERT INTO customer_messages (id, customer_id, admin_id, message_type, subject, message, status, created_at)
             VALUES (?, ?, ?, ?, ?, ?, 'SENT', NOW())",
            [
                $messageId,
                $customerId,
                $_SESSION['user_id'],
                $data['message_type'],
                $data['subject'] ?? '',
                $messageContent
            ]
        );

        // Send the actual message based on type
        $emailSent = false;

        if ($data['message_type'] === 'email' || $data['message_type'] === 'both') {
            // Send email using our enhanced SMTP system
            $emailSubject = $data['subject'] ?? 'Message from ' . APP_NAME;

            // Create HTML email content
            $htmlContent = createCustomerMessageEmail($customer, $emailSubject, $messageContent);

            $emailSent = sendSMTPEmail(
                $customer['email'],
                $emailSubject,
                $htmlContent,
                [
                    'from_name' => APP_NAME,
                    'from_email' => SMTP_FROM_EMAIL,
                    'is_html' => true
                ]
            );

            if (!$emailSent) {
                error_log("Failed to send email to customer: " . $customer['email']);
                $sendResult = false;
            }
        }

        if (($data['message_type'] === 'sms' || $data['message_type'] === 'both') && $customer['phone']) {
            // SMS sending would be implemented here when SMS service is available
            // For now, we'll mark as successful but log that SMS is not implemented
            error_log("SMS sending not yet implemented for: " . $customer['phone']);
            // SMS is not implemented yet, so we don't affect the overall success status
        }

        // Update message status based on actual sending results
        $finalStatus = 'SENT';
        $overallSuccess = true;

        if (!$emailSent && ($data['message_type'] === 'email' || $data['message_type'] === 'both')) {
            $finalStatus = 'FAILED';
            $overallSuccess = false;
        }

        // Update the message record with final status
        $database->query(
            "UPDATE customer_messages SET status = ?, updated_at = NOW() WHERE id = ?",
            [$finalStatus, $messageId]
        );

        // Return actual success status based on email sending results
        if ($overallSuccess) {
            return ['success' => true, 'message_id' => $messageId];
        } else {
            return ['success' => false, 'error' => 'Failed to send email. Please check email configuration.', 'message_id' => $messageId];
        }

    } catch (Exception $e) {
        error_log("Customer message error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to send message'];
    }
}

/**
 * Create HTML email content for customer messages
 */
function createCustomerMessageEmail($customer, $subject, $message) {
    $appName = APP_NAME;
    $currentYear = date('Y');

    // Convert plain text message to HTML with line breaks
    $htmlMessage = nl2br(htmlspecialchars($message));

    $htmlContent = "
    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>{$subject}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
            .header { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 30px; text-align: center; }
            .header h1 { margin: 0; font-size: 28px; font-weight: bold; }
            .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }
            .content { padding: 30px; }
            .message-content { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
            .customer-info { background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0; }
            .footer { background-color: #0f172a; color: white; padding: 20px; text-align: center; font-size: 14px; }
            .footer a { color: #f59e0b; text-decoration: none; }
            .button { display: inline-block; background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 15px 0; }
            .divider { height: 1px; background-color: #e2e8f0; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class='container'>
            <!-- Header -->
            <div class='header'>
                <h1>{$appName}</h1>
                <p>Personal Message for You</p>
            </div>

            <!-- Content -->
            <div class='content'>
                <h2 style='color: #0f172a; margin-top: 0;'>Hello " . htmlspecialchars($customer['name']) . "!</h2>

                <p>We hope this message finds you well. Our team has sent you a personal message:</p>

                <!-- Message Content -->
                <div class='message-content'>
                    <h3 style='color: #f59e0b; margin-top: 0;'>{$subject}</h3>
                    <div style='font-size: 16px; line-height: 1.6;'>
                        {$htmlMessage}
                    </div>
                </div>

                <!-- Customer Info -->
                <div class='customer-info'>
                    <h4 style='margin-top: 0; color: #1976d2;'>Your Account Information</h4>
                    <p><strong>Name:</strong> " . htmlspecialchars($customer['name']) . "</p>
                    <p><strong>Email:</strong> " . htmlspecialchars($customer['email']) . "</p>
                    <p><strong>Loyalty Points:</strong> " . number_format($customer['points']) . " points</p>
                </div>

                <div class='divider'></div>

                <p>If you have any questions or need assistance, please don't hesitate to contact us.</p>

                <div style='text-align: center;'>
                    <a href='" . getBaseUrl() . "' class='button'>Visit Our Website</a>
                </div>
            </div>

            <!-- Footer -->
            <div class='footer'>
                <p><strong>{$appName}</strong></p>
                <p>Your trusted beauty and wellness partner</p>
                <p>
                    <a href='" . getBaseUrl() . "'>Website</a> |
                    <a href='" . getBaseUrl() . "/contact'>Contact Us</a> |
                    <a href='" . getBaseUrl() . "/services'>Our Services</a>
                </p>
                <p style='margin-top: 15px; font-size: 12px; opacity: 0.8;'>
                    © {$currentYear} {$appName}. All rights reserved.
                </p>
            </div>
        </div>
    </body>
    </html>";

    return $htmlContent;
}
