<?php
/**
 * Revolutionary Blog Page - Completely New UI Design
 * Advanced Medical Aesthetics Knowledge Hub
 * Redolence Medi Aesthetics - Professional Medical Beauty
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/blog_functions.php';

// Check if we're viewing a single blog post
$slug = $_GET['slug'] ?? null;
$search = $_GET['search'] ?? '';
$singlePost = null;

if ($slug) {
    // Get the single blog post by slug
    $singlePost = $database->fetch(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.slug = ? AND bp.status = 'published'",
        [$slug]
    );

    // If post not found, redirect to blog index
    if (!$singlePost) {
        redirect('/blog');
    }

    $pageTitle = $singlePost['title'] . ' - Redolence Blog';
} else {
    // Get search and pagination parameters
    $page = (int)($_GET['page'] ?? 1);
    $limit = 8; // Changed for new layout
    $offset = ($page - 1) * $limit;

    // Build search query
    $searchCondition = '';
    $searchConditionCount = '';
    $searchParams = [];
    if (!empty($search)) {
        $searchCondition = " AND (bp.title LIKE ? OR bp.summary LIKE ? OR bp.full_content LIKE ?)";
        $searchConditionCount = " AND (title LIKE ? OR summary LIKE ? OR full_content LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $searchParams = [$searchTerm, $searchTerm, $searchTerm];
    }

    // Get total posts count
    $totalPosts = $database->fetch(
        "SELECT COUNT(*) as count FROM blog_posts WHERE status = 'published'" . $searchConditionCount,
        $searchParams
    )['count'];

    // Get featured posts (latest 3 posts)
    $featuredPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT 3"
    );

    // Get regular blog posts (excluding featured ones if on first page)
    $excludeFeatured = ($page === 1 && empty($search)) ? " AND bp.id NOT IN ('" . implode("','", array_column($featuredPosts, 'id')) . "')" : '';

    $blogPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'" . $searchCondition . $excludeFeatured . "
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT $limit OFFSET $offset",
        $searchParams
    );

    $totalPages = ceil($totalPosts / $limit);
    $pageTitle = !empty($search) ? "Search: $search - Blog" : 'Medical Aesthetics Knowledge Hub - Redolence';
}

// Include header
include __DIR__ . '/includes/header.php';
?>

<!-- Revolutionary Blog Design - Completely Different from Original -->
<style>
:root {
    --redolence-green: #49a75c;
    --redolence-blue: #5894d2;
    --gradient-primary: linear-gradient(135deg, #49a75c 0%, #5894d2 100%);
    --gradient-hero: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e0 100%);
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --bg-glass: rgba(255, 255, 255, 0.95);
    --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
    --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Revolutionary Layout System */
.blog-revolution {
    min-height: 100vh;
    background: var(--gradient-hero);
    position: relative;
    overflow-x: hidden;
}

/* Blog Container */
.blog-revolution {
    position: relative;
}

/* Revolutionary Hero - Magazine Style */
.magazine-hero {
    padding: 120px 0 80px;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e0 100%);
}

.hero-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.hero-text {
    position: relative;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: var(--gradient-primary);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 24px;
    box-shadow: var(--shadow-soft);
}

.hero-title {
    font-size: clamp(3rem, 8vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    color: var(--text-primary);
    margin-bottom: 24px;
}

.hero-title .highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 500px;
}

/* Revolutionary Search Design */
.hero-search {
    position: relative;
    max-width: 600px;
}

.search-wrapper {
    position: relative;
    background: white;
    border-radius: 60px;
    padding: 8px;
    box-shadow: var(--shadow-medium);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.search-wrapper:focus-within {
    border-color: var(--redolence-green);
    box-shadow: var(--shadow-strong);
    transform: translateY(-2px);
}

.search-input {
    width: 100%;
    padding: 20px 60px 20px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    background: transparent;
    color: var(--text-primary);
    outline: none;
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--gradient-primary);
    border: none;
    border-radius: 50px;
    padding: 16px 24px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: var(--shadow-medium);
}

/* Hero Visual Grid */
.hero-visual {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.visual-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    width: 100%;
    height: 100%;
}

.visual-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 30px;
    box-shadow: var(--shadow-soft);
    border: 1px solid rgba(255, 255, 255, 0.8);
    transition: all 0.5s ease;
    position: relative;
    overflow: hidden;
}

.visual-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.5s ease;
}

.visual-card:hover::before {
    transform: scaleX(1);
}

.visual-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-strong);
    background: rgba(255, 255, 255, 1);
}

.card-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 12px rgba(73, 167, 92, 0.3);
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.card-desc {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 60px;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .magazine-hero {
        padding: 100px 0 60px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    }

    .hero-content {
        padding: 0 20px;
        gap: 40px;
    }

    .hero-visual {
        height: auto;
        min-height: 400px;
    }

    .visual-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .visual-card {
        padding: 24px;
        min-height: 140px;
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(0, 0, 0, 0.08);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .card-icon {
        width: 44px;
        height: 44px;
        font-size: 18px;
        margin-bottom: 14px;
        box-shadow: 0 4px 16px rgba(73, 167, 92, 0.25);
    }

    .card-title {
        font-size: 17px;
        margin-bottom: 8px;
        color: var(--text-primary);
        font-weight: 700;
        text-shadow: none;
    }

    .card-desc {
        font-size: 14px;
        line-height: 1.5;
        color: var(--text-secondary);
        text-shadow: none;
    }
}

@media (max-width: 480px) {
    .magazine-hero {
        padding: 100px 0 40px;
    }

    .hero-content {
        padding: 0 15px;
        gap: 30px;
    }

    .hero-title {
        font-size: 2.5rem;
        margin-bottom: 16px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    .visual-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .visual-card {
        padding: 16px;
        min-height: 100px;
        background: white;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .card-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
        margin-bottom: 10px;
        background: var(--gradient-primary);
        color: white;
    }

    .card-title {
        font-size: 15px;
        margin-bottom: 5px;
        color: var(--text-primary);
        font-weight: 700;
    }

    .card-desc {
        font-size: 12px;
        line-height: 1.3;
        color: var(--text-secondary);
    }

    .search-wrapper {
        padding: 6px;
    }

    .search-input {
        padding: 16px 50px 16px 20px;
        font-size: 14px;
    }

    .search-btn {
        padding: 12px 20px;
        font-size: 14px;
    }
}
</style>

<!-- Revolutionary Blog Container -->
<div class="blog-revolution">


    <?php if ($singlePost): ?>
        <!-- Revolutionary Single Post Layout - Immersive Design -->
        <div class="immersive-post">
            <!-- Full-Screen Header -->
            <div class="post-hero-immersive">
                <?php if ($singlePost['image_url']): ?>
                    <div class="hero-image-full">
                        <img src="<?= htmlspecialchars(getBlogImageUrl($singlePost['image_url'])) ?>"
                             alt="<?= htmlspecialchars($singlePost['title']) ?>">
                        <div class="hero-overlay"></div>
                    </div>
                <?php endif; ?>

                <div class="hero-content-floating">
                    <div class="post-meta-pills">
                        <span class="meta-pill">
                            👤 <?= htmlspecialchars($singlePost['author_name'] ?? 'Redolence Team') ?>
                        </span>
                        <span class="meta-pill">
                            📅 <?= $singlePost['publish_date'] ? date('M j, Y', strtotime($singlePost['publish_date'])) : date('M j, Y', strtotime($singlePost['created_at'])) ?>
                        </span>
                        <span class="meta-pill">
                            ⏱️ <?php
                            $wordCount = str_word_count(strip_tags($singlePost['full_content']));
                            $readTime = max(1, ceil($wordCount / 200));
                            echo "$readTime min read";
                            ?>
                        </span>
                    </div>

                    <h1 class="post-title-hero"><?= htmlspecialchars($singlePost['title']) ?></h1>

                    <?php if (!empty($singlePost['summary'])): ?>
                        <p class="post-summary-hero"><?= htmlspecialchars($singlePost['summary']) ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Revolutionary Article Content -->
            <div class="article-container-new">
                <div class="article-content-wrapper">
                    <div class="article-body-new">
                        <?= $singlePost['full_content'] ?>
                    </div>

                    <!-- Floating Social Actions -->
                    <div class="social-actions-floating">
                        <button class="social-btn share-btn" title="Share">📤</button>
                        <button class="social-btn bookmark-btn" title="Bookmark">🔖</button>
                        <button class="social-btn like-btn" title="Like">❤️</button>
                    </div>
                </div>

                <!-- Back Navigation -->
                <div class="back-navigation">
                    <a href="<?= getBasePath() ?>/blog" class="back-btn-new">
                        ← Back to Knowledge Hub
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Revolutionary Magazine-Style Hero -->
        <div class="magazine-hero">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-badge">
                        ⭐ Medical Aesthetics Hub
                    </div>

                    <h1 class="hero-title">
                        Discover the <span class="highlight">Future</span> of Medical Beauty
                    </h1>

                    <p class="hero-subtitle">
                        Explore cutting-edge treatments, expert insights, and revolutionary techniques that are transforming the world of medical aesthetics.
                    </p>

                    <!-- Revolutionary Search -->
                    <form method="GET" action="<?= getBasePath() ?>/blog" class="hero-search">
                        <div class="search-wrapper">
                            <input
                                type="text"
                                name="search"
                                value="<?= htmlspecialchars($search) ?>"
                                placeholder="Search treatments, procedures, expert advice..."
                                class="search-input"
                            >
                            <button type="submit" class="search-btn">
                                🔍 Explore
                            </button>
                        </div>
                    </form>
                </div>

                <div class="hero-visual">
                    <div class="visual-grid">
                        <div class="visual-card">
                            <div class="card-icon">🏥</div>
                            <h3 class="card-title">Expert Insights</h3>
                            <p class="card-desc">Professional guidance from certified medical aesthetics specialists</p>
                        </div>

                        <div class="visual-card">
                            <div class="card-icon">💡</div>
                            <h3 class="card-title">Latest Innovations</h3>
                            <p class="card-desc">Cutting-edge treatments and breakthrough technologies</p>
                        </div>

                        <div class="visual-card">
                            <div class="card-icon">📚</div>
                            <h3 class="card-title">Educational Content</h3>
                            <p class="card-desc">Comprehensive guides and evidence-based information</p>
                        </div>

                        <div class="visual-card">
                            <div class="card-icon">📊</div>
                            <h3 class="card-title">Real Results</h3>
                            <p class="card-desc">Case studies and patient transformation stories</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revolutionary Content Sections -->

        <!-- Spotlight Section - Completely Different Layout -->
        <?php if ($page === 1 && empty($search) && !empty($featuredPosts)): ?>
        <section class="spotlight-revolution">
            <div class="spotlight-wrapper">
                <div class="section-header-new">
                    <div class="header-badge-new">✨ Featured Spotlight</div>
                    <h2 class="section-title-new">
                        <span class="title-word">Latest</span>
                        <span class="title-word highlight">Breakthroughs</span>
                    </h2>
                </div>

                <!-- Revolutionary Asymmetric Layout -->
                <div class="asymmetric-grid">
                    <?php $mainPost = $featuredPosts[0]; ?>
                    <!-- Dominant Feature Article -->
                    <div class="feature-dominant">
                        <div class="feature-card-new">
                            <?php if ($mainPost['image_url']): ?>
                                <div class="feature-image-new">
                                    <img src="<?= htmlspecialchars(getBlogImageUrl($mainPost['image_url'])) ?>"
                                         alt="<?= htmlspecialchars($mainPost['title']) ?>">
                                    <div class="image-gradient-new"></div>
                                </div>
                            <?php endif; ?>

                            <div class="feature-content-new">
                                <div class="feature-meta-new">
                                    <span class="trending-badge">🔥 Trending Now</span>
                                    <span class="date-badge">
                                        <?= $mainPost['publish_date'] ? date('M j', strtotime($mainPost['publish_date'])) : date('M j', strtotime($mainPost['created_at'])) ?>
                                    </span>
                                </div>

                                <h3 class="feature-title-new">
                                    <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>">
                                        <?= htmlspecialchars($mainPost['title']) ?>
                                    </a>
                                </h3>

                                <?php if (!empty($mainPost['summary'])): ?>
                                    <p class="feature-excerpt-new"><?= htmlspecialchars($mainPost['summary']) ?></p>
                                <?php endif; ?>

                                <div class="feature-footer-new">
                                    <div class="author-info-new">
                                        <div class="author-avatar-new">👤</div>
                                        <span class="author-name-new"><?= htmlspecialchars($mainPost['author_name'] ?? 'Redolence Team') ?></span>
                                    </div>

                                    <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>" class="read-btn-new">
                                        Read Article →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Compact Side Articles -->
                    <div class="feature-sidebar-new">
                        <?php for ($i = 1; $i < min(3, count($featuredPosts)); $i++): ?>
                            <?php $post = $featuredPosts[$i]; ?>
                            <div class="compact-article">
                                <?php if ($post['image_url']): ?>
                                    <div class="compact-image">
                                        <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>"
                                             alt="<?= htmlspecialchars($post['title']) ?>">
                                    </div>
                                <?php endif; ?>

                                <div class="compact-content">
                                    <div class="compact-meta">
                                        <span class="category-tag">Featured</span>
                                        <span class="time-tag">
                                            <?= $post['publish_date'] ? date('M j', strtotime($post['publish_date'])) : date('M j', strtotime($post['created_at'])) ?>
                                        </span>
                                    </div>

                                    <h4 class="compact-title">
                                        <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>">
                                            <?= htmlspecialchars($post['title']) ?>
                                        </a>
                                    </h4>

                                    <div class="compact-author">
                                        👤 <?= htmlspecialchars($post['author_name'] ?? 'Redolence Team') ?>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Revolutionary Masonry-Style Grid -->
        <?php if (!empty($blogPosts)): ?>
        <section class="masonry-revolution">
            <div class="masonry-wrapper">
                <?php if ($page === 1 && empty($search)): ?>
                    <div class="section-header-masonry">
                        <h2 class="masonry-title">
                            <span class="title-accent">Latest</span> Insights
                        </h2>
                        <p class="masonry-subtitle">Discover the most recent advances in medical aesthetics</p>
                    </div>
                <?php elseif (!empty($search)): ?>
                    <div class="search-results-header-new">
                        <h2 class="results-title-new">
                            Search Results for "<span class="search-term-new"><?= htmlspecialchars($search) ?></span>"
                        </h2>
                        <p class="results-count-new"><?= $totalPosts ?> article<?= $totalPosts !== 1 ? 's' : '' ?> found</p>
                    </div>
                <?php endif; ?>

                <!-- Revolutionary Pinterest-Style Grid -->
                <div class="pinterest-grid">
                    <?php foreach ($blogPosts as $index => $post): ?>
                        <article class="pinterest-item <?= $index % 3 === 0 ? 'pinterest-tall' : '' ?>">
                            <div class="pinterest-card">
                                <?php if ($post['image_url']): ?>
                                    <div class="pinterest-image">
                                        <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>"
                                             alt="<?= htmlspecialchars($post['title']) ?>"
                                             loading="lazy">
                                        <div class="image-overlay-pinterest"></div>
                                    </div>
                                <?php endif; ?>

                                <div class="pinterest-content">
                                    <div class="pinterest-meta">
                                        <span class="meta-category-new">Medical Aesthetics</span>
                                        <span class="meta-date-new">
                                            <?= $post['publish_date'] ? date('M j, Y', strtotime($post['publish_date'])) : date('M j, Y', strtotime($post['created_at'])) ?>
                                        </span>
                                    </div>

                                    <h3 class="pinterest-title">
                                        <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>">
                                            <?= htmlspecialchars($post['title']) ?>
                                        </a>
                                    </h3>

                                    <?php if (!empty($post['summary'])): ?>
                                        <p class="pinterest-excerpt"><?= htmlspecialchars($post['summary']) ?></p>
                                    <?php endif; ?>

                                    <div class="pinterest-footer">
                                        <div class="author-badge-new">
                                            👤 <span><?= htmlspecialchars($post['author_name'] ?? 'Redolence Team') ?></span>
                                        </div>

                                        <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>" class="continue-btn">
                                            Continue Reading →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Revolutionary Pagination -->
        <?php if ($totalPages > 1): ?>
        <section class="pagination-revolution">
            <div class="pagination-wrapper-new">
                <div class="pagination-controls">
                    <!-- Previous Button -->
                    <?php if ($page > 1): ?>
                        <a href="<?= getBasePath() ?>/blog?page=<?= $page - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>"
                           class="pagination-btn-new pagination-prev-new">
                            ← Previous
                        </a>
                    <?php endif; ?>

                    <!-- Page Numbers -->
                    <div class="pagination-numbers-new">
                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        for ($i = $startPage; $i <= $endPage; $i++):
                        ?>
                            <?php if ($i == $page): ?>
                                <span class="pagination-number-new active"><?= $i ?></span>
                            <?php else: ?>
                                <a href="<?= getBasePath() ?>/blog?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>"
                                   class="pagination-number-new"><?= $i ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>
                    </div>

                    <!-- Next Button -->
                    <?php if ($page < $totalPages): ?>
                        <a href="<?= getBasePath() ?>/blog?page=<?= $page + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>"
                           class="pagination-btn-new pagination-next-new">
                            Next →
                        </a>
                    <?php endif; ?>
                </div>

                <div class="pagination-info-new">
                    <span>Page <?= $page ?> of <?= $totalPages ?></span>
                    <span class="separator">•</span>
                    <span><?= $totalPosts ?> total articles</span>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- No Results State -->
        <?php if (empty($blogPosts) && (empty($featuredPosts) || $page > 1 || !empty($search))): ?>
        <section class="no-results-revolution">
            <div class="no-results-wrapper">
                <div class="no-results-visual-new">
                    <div class="empty-icon">🔍</div>
                </div>

                <div class="no-results-content-new">
                    <h3 class="no-results-title-new">
                        <?= !empty($search) ? 'No articles found' : 'No articles available' ?>
                    </h3>
                    <p class="no-results-text-new">
                        <?= !empty($search) ? 'Try adjusting your search terms or explore our featured content.' : 'Check back soon for new medical aesthetics insights and expert guidance.' ?>
                    </p>

                    <?php if (!empty($search)): ?>
                        <div class="no-results-actions-new">
                            <a href="<?= getBasePath() ?>/blog" class="action-btn-primary-new">
                                ⊞ Browse All Articles
                            </a>
                            <button class="action-btn-secondary-new" onclick="document.querySelector('.search-input').focus()">
                                🔍 Try New Search
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
/* Revolutionary Content Styling - Completely New */

/* Single Post Revolutionary Styles */
.immersive-post {
    background: white;
}

.post-hero-immersive {
    position: relative;
    height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-image-full {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hero-image-full img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3));
}

.hero-content-floating {
    position: relative;
    z-index: 10;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 40px;
}

.post-meta-pills {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 32px;
    flex-wrap: wrap;
}

.meta-pill {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.post-title-hero {
    font-size: clamp(2.5rem, 8vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 24px;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.post-summary-hero {
    font-size: 1.25rem;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.article-container-new {
    max-width: 800px;
    margin: 0 auto;
    padding: 80px 40px;
    position: relative;
}

.article-body-new {
    font-size: 1.125rem;
    line-height: 1.8;
    color: var(--text-primary);
}

.article-body-new h2 {
    font-size: 2rem;
    font-weight: 800;
    margin: 3rem 0 1.5rem;
    color: var(--text-primary);
    position: relative;
    padding-left: 20px;
}

.article-body-new h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.article-body-new p {
    margin-bottom: 1.5rem;
}

.article-body-new a {
    color: var(--redolence-green);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.3s ease;
}

.article-body-new a:hover {
    border-bottom-color: var(--redolence-green);
}

.social-actions-floating {
    position: fixed;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 16px;
    z-index: 100;
}

.social-btn {
    width: 56px;
    height: 56px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.social-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
}

.back-navigation {
    text-align: center;
    margin-top: 60px;
}

.back-btn-new {
    display: inline-flex;
    align-items: center;
    background: var(--gradient-primary);
    color: white;
    padding: 16px 32px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.back-btn-new:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Spotlight Section */
.spotlight-revolution {
    padding: 100px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
}

.spotlight-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.section-header-new {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge-new {
    display: inline-block;
    background: var(--gradient-primary);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.section-title-new {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 900;
    line-height: 1.1;
    color: var(--text-primary);
}

.title-word {
    display: inline-block;
    margin-right: 0.5em;
}

.title-word.highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Asymmetric Grid */
.asymmetric-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
}

.feature-dominant {
    position: relative;
}

.feature-card-new {
    background: white;
    border-radius: 32px;
    overflow: hidden;
    box-shadow: var(--shadow-strong);
    transition: all 0.5s ease;
    position: relative;
}

.feature-card-new:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
}

.feature-image-new {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.feature-image-new img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.7s ease;
}

.feature-card-new:hover .feature-image-new img {
    transform: scale(1.1);
}

.image-gradient-new {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.feature-content-new {
    padding: 40px;
}

.feature-meta-new {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.trending-badge {
    background: var(--gradient-primary);
    color: white;
    padding: 6px 16px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.date-badge {
    color: var(--text-muted);
    font-size: 14px;
    font-weight: 600;
}

.feature-title-new {
    font-size: 2rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 16px;
}

.feature-title-new a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.feature-title-new a:hover {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-excerpt-new {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 32px;
}

.feature-footer-new {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.author-info-new {
    display: flex;
    align-items: center;
    gap: 12px;
}

.author-avatar-new {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.author-name-new {
    font-weight: 600;
    color: var(--text-primary);
}

.read-btn-new {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--gradient-primary);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.read-btn-new:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Compact Side Articles */
.feature-sidebar-new {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.compact-article {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
    display: flex;
    gap: 16px;
    padding: 20px;
}

.compact-article:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.compact-image {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
}

.compact-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.compact-content {
    flex: 1;
}

.compact-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.category-tag {
    background: rgba(73, 167, 92, 0.1);
    color: var(--redolence-green);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
}

.time-tag {
    color: var(--text-muted);
    font-size: 12px;
}

.compact-title {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 8px;
}

.compact-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.compact-title a:hover {
    color: var(--redolence-green);
}

.compact-author {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-muted);
    font-size: 12px;
}

/* Masonry Section */
.masonry-revolution {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.masonry-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.section-header-masonry {
    text-align: center;
    margin-bottom: 80px;
}

.masonry-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.title-accent {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.masonry-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.search-results-header-new {
    text-align: center;
    margin-bottom: 60px;
}

.results-title-new {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.search-term-new {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.results-count-new {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Pinterest-Style Grid */
.pinterest-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    align-items: start;
}

.pinterest-item.pinterest-tall {
    grid-row: span 2;
}

.pinterest-card {
    background: white;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: all 0.5s ease;
    position: relative;
}

.pinterest-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-strong);
}

.pinterest-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.pinterest-tall .pinterest-image {
    height: 350px;
}

.pinterest-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.7s ease;
}

.pinterest-card:hover .pinterest-image img {
    transform: scale(1.1);
}

.image-overlay-pinterest {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pinterest-card:hover .image-overlay-pinterest {
    opacity: 1;
}

.pinterest-content {
    padding: 32px;
}

.pinterest-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.meta-category-new {
    background: rgba(73, 167, 92, 0.1);
    color: var(--redolence-green);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
}

.meta-date-new {
    color: var(--text-muted);
    font-size: 14px;
}

.pinterest-title {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 16px;
}

.pinterest-tall .pinterest-title {
    font-size: 1.75rem;
}

.pinterest-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.pinterest-title a:hover {
    color: var(--redolence-green);
}

.pinterest-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 24px;
}

.pinterest-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.author-badge-new {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-muted);
    font-size: 14px;
}

.continue-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--redolence-green);
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.continue-btn:hover {
    gap: 10px;
    color: var(--redolence-green-dark);
}

/* Revolutionary Pagination */
.pagination-revolution {
    padding: 80px 0;
    background: white;
}

.pagination-wrapper-new {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 24px;
}

.pagination-btn-new {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--gradient-primary);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.pagination-btn-new:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.pagination-numbers-new {
    display: flex;
    gap: 8px;
}

.pagination-number-new {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid transparent;
}

.pagination-number-new:hover,
.pagination-number-new.active {
    background: var(--gradient-primary);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-soft);
}

.pagination-info-new {
    color: var(--text-muted);
    font-size: 14px;
}

.separator {
    margin: 0 8px;
}

/* No Results State */
.no-results-revolution {
    padding: 120px 0;
    text-align: center;
}

.no-results-wrapper {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 40px;
}

.no-results-visual-new {
    margin-bottom: 40px;
}

.empty-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 48px;
    color: var(--redolence-green);
}

.no-results-title-new {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.no-results-text-new {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 40px;
}

.no-results-actions-new {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn-primary-new,
.action-btn-secondary-new {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.action-btn-primary-new {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-soft);
}

.action-btn-primary-new:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.action-btn-secondary-new {
    background: white;
    color: var(--redolence-green);
    border: 2px solid var(--redolence-green);
}

.action-btn-secondary-new:hover {
    background: var(--redolence-green);
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .asymmetric-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .pinterest-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
    }

    .social-actions-floating {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-top: 40px;
        transform: none;
    }
}

@media (max-width: 768px) {
    .hero-content {
        padding: 0 20px;
    }

    .spotlight-wrapper,
    .masonry-wrapper {
        padding: 0 20px;
    }

    .pinterest-grid {
        grid-template-columns: 1fr;
    }

    .pagination-controls {
        flex-wrap: wrap;
        gap: 12px;
    }

    .post-meta-pills {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .hero-content-floating {
        padding: 0 20px;
    }
}
</style>

<script>
// Revolutionary Interactive Features
document.addEventListener('DOMContentLoaded', function() {

    // Search Input Enhancement
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    }
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
